/**
 * Enhanced Application Response Tests
 * 
 * Tests for the enhanced GET /applications endpoint that includes:
 * - agent_ids array with full agent details
 * - service_name field from resolved service names
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ApplicationController } from '../../src/application/controllers/application.controller';
import { ApplicationService } from '../../src/application/application.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { IJWTPayload } from '../../src/types/auth';
import { ApplicationStatus, PriorityLevel } from '@prisma/client';

describe('Enhanced Application Response', () => {
  let controller: ApplicationController;
  let applicationService: ApplicationService;
  let transformerService: ApplicationTransformerService;

  // Mock data for testing
  const mockApplicationData = {
    id: 'app_123',
    application_number: 'IMM-2024-000001',
    service_type: 'immigration',
    service_id: 'service_123',
    status: ApplicationStatus.Under_Review,
    priority_level: PriorityLevel.Medium,
    current_step: '2',
    agent_ids: ['agent_1', 'agent_2'],
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-02'),
    estimated_completion: new Date('2024-02-01'),
    user: {
      id: 'user_123',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    workflow_template: {
      id: 'template_123',
      name: 'Immigration Workflow',
      description: 'Standard immigration process',
      workflowTemplate: [
        { stageOrder: 1, stageName: 'Initial Review' },
        { stageOrder: 2, stageName: 'Document Collection' },
        { stageOrder: 3, stageName: 'Final Review' },
      ],
    },
    agent_details: [
      { id: 'agent_1', name: 'Agent Smith', email: '<EMAIL>' },
      { id: 'agent_2', name: 'Agent Jones', email: '<EMAIL>' },
    ],
    service_name: 'Work Permit Application',
  };

  const mockUser: IJWTPayload = {
    id: 'user_123',
    email: '<EMAIL>',
    sub: { name: 'John Doe' },
    iat: 1234567890,
    exp: 1234567890,
    tokenType: 'user',
  };

  const mockApplicationService = {
    getApplicationsWithTransformation: jest.fn(),
    enhanceApplicationsWithDetails: jest.fn(),
    resolveServiceName: jest.fn(),
  };

  const mockTransformerService = {
    createPaginatedResponse: jest.fn(),
    transformApplicationListItem: jest.fn(),
  };

  const mockPrismaService = {
    application: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    agent: {
      findMany: jest.fn(),
    },
  };

  const mockLoggerService = {
    error: jest.fn(),
    warn: jest.fn(),
    log: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      controllers: [ApplicationController],
      providers: [
        {
          provide: ApplicationService,
          useValue: mockApplicationService,
        },
        {
          provide: ApplicationTransformerService,
          useValue: mockTransformerService,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    controller = module.get<ApplicationController>(ApplicationController);
    applicationService = module.get<ApplicationService>(ApplicationService);
    transformerService = module.get<ApplicationTransformerService>(ApplicationTransformerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserApplications', () => {
    it('should return applications with enhanced agent_ids and service_name', async () => {
      // Arrange
      const expectedResponse = {
        data: [
          {
            id: 'app_123',
            application_number: 'IMM-2024-000001',
            service_type: 'immigration',
            service_name: 'Work Permit Application',
            status: 'Under_Review',
            priority_level: 'Medium',
            current_step: '2',
            numberOfSteps: 3,
            agent_ids: [
              { id: 'agent_1', name: 'Agent Smith', email: '<EMAIL>' },
              { id: 'agent_2', name: 'Agent Jones', email: '<EMAIL>' },
            ],
            user: {
              name: 'John Doe',
              email: '<EMAIL>',
            },
            estimated_completion: '2024-02-01T00:00:00.000Z',
            created_at: '2024-01-01T00:00:00.000Z',
            updated_at: '2024-01-02T00:00:00.000Z',
          },
        ],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };

      mockApplicationService.getApplicationsWithTransformation.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.getUserApplications(mockUser, {
        page: 1,
        limit: 10,
      });

      // Assert
      expect(result).toEqual(expectedResponse);
      expect(result.data[0]).toHaveProperty('agent_ids');
      expect(result.data[0]).toHaveProperty('service_name');
      expect(Array.isArray(result.data[0].agent_ids)).toBe(true);
      expect(result.data[0].agent_ids).toHaveLength(2);
      expect(result.data[0].agent_ids[0]).toHaveProperty('id');
      expect(result.data[0].agent_ids[0]).toHaveProperty('name');
      expect(result.data[0].agent_ids[0]).toHaveProperty('email');
      expect(typeof result.data[0].service_name).toBe('string');
      expect(result.data[0].service_name).toBe('Work Permit Application');
    });

    it('should handle applications with no assigned agents', async () => {
      // Arrange
      const responseWithNoAgents = {
        data: [
          {
            id: 'app_124',
            application_number: 'IMM-2024-000002',
            service_type: 'immigration',
            service_name: 'Student Visa Application',
            status: 'Draft',
            agent_ids: [], // No agents assigned
            created_at: '2024-01-01T00:00:00.000Z',
            updated_at: '2024-01-02T00:00:00.000Z',
          },
        ],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };

      mockApplicationService.getApplicationsWithTransformation.mockResolvedValue(responseWithNoAgents);

      // Act
      const result = await controller.getUserApplications(mockUser, {
        page: 1,
        limit: 10,
      });

      // Assert
      expect(result.data[0].agent_ids).toEqual([]);
      expect(Array.isArray(result.data[0].agent_ids)).toBe(true);
      expect(result.data[0]).toHaveProperty('service_name');
      expect(result.data[0].service_name).toBe('Student Visa Application');
    });

    it('should call service with correct user filters', async () => {
      // Arrange
      const queryParams = {
        page: 2,
        limit: 5,
        service_type: 'immigration',
        status: 'InProgress',
      };

      mockApplicationService.getApplicationsWithTransformation.mockResolvedValue({
        data: [],
        pagination: { total: 0, page: 2, limit: 5, totalPages: 0 },
      });

      // Act
      await controller.getUserApplications(mockUser, queryParams);

      // Assert
      expect(mockApplicationService.getApplicationsWithTransformation).toHaveBeenCalledWith(
        {
          user_id: 'user_123',
          service_type: 'immigration',
          status: 'InProgress',
        },
        2,
        5,
        undefined
      );
    });
  });

  describe('getAllApplications (Admin)', () => {
    it('should return all applications with enhanced fields for admin', async () => {
      // Arrange
      const adminResponse = {
        data: [
          {
            id: 'app_123',
            service_name: 'Work Permit Application',
            agent_ids: [
              { id: 'agent_1', name: 'Agent Smith', email: '<EMAIL>' },
            ],
          },
          {
            id: 'app_124',
            service_name: 'Student Visa Application',
            agent_ids: [],
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };

      mockApplicationService.getApplicationsWithTransformation.mockResolvedValue(adminResponse);

      // Act
      const result = await controller.getAllApplications({
        page: 1,
        limit: 10,
      });

      // Assert
      expect(result).toEqual(adminResponse);
      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toHaveProperty('agent_ids');
      expect(result.data[0]).toHaveProperty('service_name');
      expect(result.data[1]).toHaveProperty('agent_ids');
      expect(result.data[1]).toHaveProperty('service_name');
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain existing response structure while adding new fields', async () => {
      // Arrange
      const response = {
        data: [
          {
            id: 'app_123',
            application_number: 'IMM-2024-000001',
            service_type: 'immigration',
            status: 'InProgress',
            priority_level: 'Medium',
            current_step: '2',
            // New fields
            agent_ids: [{ id: 'agent_1', name: 'Agent Smith', email: '<EMAIL>' }],
            service_name: 'Work Permit Application',
            // Existing fields should still be present
            created_at: '2024-01-01T00:00:00.000Z',
            updated_at: '2024-01-02T00:00:00.000Z',
            user: { name: 'John Doe', email: '<EMAIL>' },
          },
        ],
        pagination: { total: 1, page: 1, limit: 10, totalPages: 1 },
      };

      mockApplicationService.getApplicationsWithTransformation.mockResolvedValue(response);

      // Act
      const result = await controller.getUserApplications(mockUser, { page: 1, limit: 10 });

      // Assert - Check that all existing fields are still present
      const application = result.data[0];
      expect(application).toHaveProperty('id');
      expect(application).toHaveProperty('application_number');
      expect(application).toHaveProperty('service_type');
      expect(application).toHaveProperty('status');
      expect(application).toHaveProperty('priority_level');
      expect(application).toHaveProperty('current_step');
      expect(application).toHaveProperty('created_at');
      expect(application).toHaveProperty('updated_at');
      expect(application).toHaveProperty('user');
      
      // Check new fields
      expect(application).toHaveProperty('agent_ids');
      expect(application).toHaveProperty('service_name');
    });
  });
});
