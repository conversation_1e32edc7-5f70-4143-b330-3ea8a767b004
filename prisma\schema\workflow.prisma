// Workflow Master Management Tables for Dynamic Workflow System

model workflow_master {
  id String @id @default(cuid())

  // Workflow Template Information
  name         String  // Workflow template name (required)
  description  String? // Workflow description (optional)
  is_active    <PERSON>olean @default(true) // Whether this workflow master is active

  // Audit Fields - Store admin user names for human-readable audit trail
  created_by String? // Admin user name who created this workflow master (e.g., "<PERSON>")
  updated_by String? // Admin user name who last updated this workflow master (e.g., "<PERSON>")
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Indexes for performance optimization
  @@index([name])
  @@index([is_active])
  @@index([created_at])
  @@index([updated_at])
  @@map("workflow_master")
}

model workflow_template {
  id String @id @default(cuid())

  // Template Information
  name        String  // Template name (required)
  description String? // Template description (optional)
  serviceType String  // Service type: 'immigration', 'training', 'packages', 'consulting'
  serviceId   String? // Reference to specific service (optional)
  isActive    Boolean @default(true) // Whether this template is active

  // Workflow Template JSON Structure
  workflowTemplate Json // JSON array containing workflow stages and configuration

  // Audit Fields - Store admin user names for human-readable audit trail
  createdBy String? // Admin user name who created this template (e.g., "John Smith")
  updatedBy String? // Admin user name who last updated this template (e.g., "Jane Doe")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  applications application[]

  // Indexes for performance optimization
  @@index([name])
  @@index([serviceType])
  @@index([isActive])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("workflow_template")
}
