/**
 * Application Transformer Service
 *
 * Transforms raw database application data into structured API responses.
 * Handles data formatting for different API endpoints (list vs detail views).
 */

import { Injectable, Logger } from '@nestjs/common';

export interface ApplicationListItem {
  id: string;
  application_number: string;
  service_type: string;
  status: string;
  service_name: string;
  priority_level: string;
  current_step: string;
  numberOfSteps: number;
  agent_ids: {
    id: string;
    name: string;
    email: string;
  }[];
  assigned_agent?: {
    id: string;
    name: string;
    email: string;
  };
  user?: {
    name: string;
    email: string;
    mobile?: string;
  };
  guest?: {
    name: string;
    email: string;
    mobile: string;
  };
  estimated_completion: string | null;
  created_at: string;
  updated_at: string;
}

export interface ApplicationDocument {
  id: string;
  fileName: string;
  fileUrl: string;
  required: boolean;
  status: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string;
  requestReason?: string;
  uploadDate: string;
  updatedAt: string;
  uploadedBy?: string;
}

export interface ApplicationFormField {
  id: string;
  fieldName: string;
  fieldType: string;
  required: boolean;
  fieldValue?: string;
  fieldOptions?: any;
  showToClient: boolean;
}

export interface ApplicationStepData {
  stageOrder: number;
  stageName: string;
  documentsRequired: boolean;
  customFormRequired: boolean;
  documents: ApplicationDocument[];
  customForm: ApplicationFormField[];
}

export interface ApplicationDetailsResponse {
  id: string;
  application_number: string;
  service_type: string;
  service_name: string;
  note: string;
  status: string;
  priority_level: string;
  current_step: string;
  numberOfSteps: number;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  guest?: {
    name: string;
    email: string;
    mobile: string;
  };
  workflow_template: {
    id: string;
    name: string;
    description: string;
  };
  steps: ApplicationStepData[];
  assigned_to?: string;
  estimated_completion: string | null;
  created_at: string;
  updated_at: string;
}

@Injectable()
export class ApplicationTransformerService {
  private readonly logger = new Logger(ApplicationTransformerService.name);

  /**
   * Transform application data for list view (minimal data)
   */
  transformApplicationListItem(application: any): ApplicationListItem {
    const transformed: ApplicationListItem = {
      id: application.id,
      application_number: application.application_number,
      service_type: application.service_type,
      service_name: application.service_name || `${application.service_type} service`,
      status: application.status,
      priority_level: application.priority_level,
      current_step: application.current_step,
      numberOfSteps: this.calculateNumberOfSteps(
        application.workflow_template?.workflowTemplate,
      ),
      agent_ids: application.agent_details || [],
      assigned_agent: application.assigned_agent,
      estimated_completion:
        application.estimated_completion?.toISOString() || null,
      created_at: application.created_at.toISOString(),
      updated_at: application.updated_at.toISOString(),
    };

    // Add user or guest information
    if (application.user) {
      transformed.user = {
        name: application.user.name,
        email: application.user.email,
        mobile: application.user.mobileNo,
      };
    } else if (application.guest_name && application.guest_email) {
      transformed.guest = {
        name: application.guest_name,
        email: application.guest_email,
        mobile: application.guest_mobile,
      };
    }

    return transformed;
  }

  /**
   * Transform application data for detail view (complete data)
   */
  transformApplicationDetails(application: any): ApplicationDetailsResponse {
    const transformed: ApplicationDetailsResponse = {
      id: application.id,
      application_number: application.application_number,
      service_type: application.service_type,
      service_name:
        application.service_name || `${application.service_type} service`,
      status: application.status,
      note: application.note || '',
      priority_level: application.priority_level,
      current_step: application.current_step,
      numberOfSteps: this.calculateNumberOfSteps(
        application.workflow_template?.workflowTemplate,
      ),
      workflow_template: {
        id: application.workflow_template?.id || '',
        name: application.workflow_template?.name || '',
        description: application.workflow_template?.description || '',
      },
      steps: this.transformWorkflowSteps(
        application.workflow_template?.workflowTemplate || [],
        application.form_data || [],
        application.documents || [],
      ),
      estimated_completion:
        application.estimated_completion?.toISOString() || null,
      assigned_to: application.assigned_to || null,
      created_at: application.created_at.toISOString(),
      updated_at: application.updated_at.toISOString(),
    };

    // Add user or guest information
    if (application.user) {
      transformed.user = {
        id: application.user.id,
        name: application.user.name,
        email: application.user.email,
      };
    } else if (application.guest_name && application.guest_email) {
      transformed.guest = {
        name: application.guest_name,
        email: application.guest_email,
        mobile: application.guest_mobile,
      };
    }

    return transformed;
  }

  /**
   * Transform workflow template steps with form data and documents
   */
  private transformWorkflowSteps(
    workflowTemplate: any[],
    formData: any[],
    documents: any[],
  ): ApplicationStepData[] {
    if (!Array.isArray(workflowTemplate)) {
      return [];
    }

    return workflowTemplate.map((stage) => {
      const stageOrder = stage.stageOrder || 1;

      // Get form fields for this stage
      const stageFormData = formData.filter(
        (field) => field.stage_order === stageOrder,
      );

      // Get documents for this stage
      const stageDocuments = documents.filter(
        (doc) => doc.stage_order === stageOrder,
      );

      return {
        stageOrder,
        stageName: stage.stageName || `Stage ${stageOrder}`,
        documentsRequired: Boolean(stage.documentsRequired),
        customFormRequired: Boolean(stage.customFormRequired),
        documents: this.transformDocuments(stageDocuments),
        customForm: this.transformFormFields(stageFormData),
      };
    });
  }

  /**
   * Transform document data
   */
  private transformDocuments(documents: any[]): ApplicationDocument[] {
    return documents.map((doc) => ({
      id: doc.id,
      fileName: doc.file_name || doc.document?.original_filename || '',
      fileUrl: doc.file_url || doc.document?.file_path || '',
      required: Boolean(doc.required),
      status: this.normalizeDocumentStatus(doc.status),
      rejectionReason: doc.rejection_reason || undefined,
      requestReason: doc.request_reason || undefined,
      uploadDate: (doc.upload_date || doc.created_at).toISOString(),
      updatedAt: doc.updated_at.toISOString(),
      uploadedBy: doc.uploaded_by || undefined,
    }));
  }

  /**
   * Transform form field data
   */
  private transformFormFields(formFields: any[]): ApplicationFormField[] {
    return formFields.map((field) => ({
      id: field.id,
      fieldName: field.field_name,
      fieldType: field.field_type,
      required: Boolean(field.required),
      fieldValue: field.field_value || undefined,
      fieldOptions: field.field_options || undefined,
      showToClient: Boolean(field.show_to_client),
    }));
  }

  /**
   * Normalize document status to API format
   */
  private normalizeDocumentStatus(
    status: string,
  ): 'pending' | 'approved' | 'rejected' {
    if (!status) return 'pending';

    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus.includes('approved')) return 'approved';
    if (normalizedStatus.includes('rejected')) return 'rejected';
    return 'pending';
  }

  /**
   * Calculate the number of steps/stages in a workflow template
   */
  private calculateNumberOfSteps(workflowTemplate: any[]): number {
    try {
      if (!Array.isArray(workflowTemplate)) {
        return 0;
      }
      return workflowTemplate.length;
    } catch (error) {
      this.logger.warn(
        'Failed to calculate number of steps from workflow template',
        error,
      );
      return 0;
    }
  }

  /**
   * Transform multiple applications for list response
   */
  transformApplicationList(applications: any[]): ApplicationListItem[] {
    return applications.map((app) => this.transformApplicationListItem(app));
  }

  /**
   * Create paginated response
   */
  createPaginatedResponse(
    applications: any[],
    total: number,
    page: number,
    limit: number,
  ) {
    return {
      data: this.transformApplicationList(applications),
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
