/**
 * Workflow Template Service
 *
 * This service provides comprehensive workflow template management functionality
 * for the Career Ireland platform. It handles CRUD operations, validation,
 * usage tracking, and business logic for workflow templates.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import {
  CreateWorkflowTemplateDto,
  UpdateWorkflowTemplateDto,
  WorkflowTemplateFiltersDto,
  PaginatedWorkflowTemplateResponseDto,
  WorkflowTemplateResponseDto,
  WorkflowTemplateUsageResponseDto,
  SetDefaultTemplateDto,
} from './dto/workflow-template.dto';

import { IJWTPayload } from '../types/auth';

@Injectable()
export class WorkflowTemplateService {
  private readonly logger = new Logger(WorkflowTemplateService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new workflow template
   *
   * @param dto - Workflow template creation data
   * @param adminUser - Admin user from JWT payload (optional for backward compatibility)
   */
  async create(
    dto: CreateWorkflowTemplateDto,
    adminUser?: IJWTPayload | string,
  ): Promise<WorkflowTemplateResponseDto> {
    try {
      this.logger.log(`Creating workflow template: ${dto.name}`);

      // Extract admin name from user object or fallback to string (backward compatibility)
      const adminName = this.extractAdminName(adminUser);

      // Validate workflow template structure
      this.validateWorkflowTemplate(dto.workflowTemplate);

      // Validate package exists if packageId is provided
      if (dto.packageId) {
        await this.validatePackageExists(dto.packageId);
      }

      // Check if workflow template with same name already exists
      const existingTemplate = await this.prisma.workflow_template.findFirst({
        where: {
          name: {
            equals: dto.name,
            mode: 'insensitive',
          },
        },
      });

      if (existingTemplate) {
        throw new ConflictException(
          `Workflow template with name "${dto.name}" already exists`,
        );
      }

      // Multiple workflow templates are now allowed per service
      // This enables flexible workflow management for different scenarios

      const workflowTemplate = await this.prisma.workflow_template.create({
        data: {
          name: dto.name,
          description: dto.description,
          serviceType: dto.serviceType,
          serviceId: dto.serviceId,
          // TODO: Re-enable after Prisma client regeneration
          // packageId: dto.packageId,
          // isDefault: dto.isDefault ?? false,
          isActive: dto.isActive ?? true,
          workflowTemplate: dto.workflowTemplate as any,
          createdBy: adminName,
        },
      });

      this.logger.log(
        `Workflow template created successfully: ${workflowTemplate.id} by ${adminName || 'Unknown Admin'}`,
      );
      return await this.mapToResponseDto(workflowTemplate);
    } catch (error) {
      this.logger.error(
        `Failed to create workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find all workflow templates with pagination and filtering
   *
   * @param filters - Filter and pagination options
   */
  async findAll(
    filters: WorkflowTemplateFiltersDto = {},
  ): Promise<PaginatedWorkflowTemplateResponseDto> {
    try {
      this.logger.log('Fetching workflow templates with filters', filters);

      const {
        search,
        serviceType,
        serviceId,
        isActive,
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = filters;

      // Build where clause
      const where: any = {};

      if (search) {
        where.OR = [
          {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            description: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ];
      }

      if (serviceType) {
        where.serviceType = serviceType;
      }

      // Filter by service ID if provided
      if (serviceId) {
        // If serviceType is immigration, validate that the service ID exists
        if (serviceType === 'immigration' || !serviceType) {
          const immigrationService = await this.prisma.immigration_service.findUnique({
            where: { id: serviceId },
          });

          if (!immigrationService) {
            throw new NotFoundException(
              `Immigration service with ID ${serviceId} not found`,
            );
          }

          // Filter workflow templates by the service ID
          where.serviceType = 'immigration';
          where.serviceId = serviceId;
        } else {
          // For other service types, just filter by serviceId without validation
          where.serviceId = serviceId;
        }
      }

      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build orderBy clause
      const orderBy: any = {};
      if (sortBy === 'serviceType') {
        orderBy.serviceType = sortOrder;
      } else if (sortBy === 'createdAt') {
        orderBy.createdAt = sortOrder;
      } else if (sortBy === 'updatedAt') {
        orderBy.updatedAt = sortOrder;
      } else {
        orderBy[sortBy] = sortOrder;
      }

      // Execute queries with package relationship
      const [templates, total] = await Promise.all([
        this.prisma.workflow_template.findMany({
          where,
          // TODO: Re-enable package include after Prisma client regeneration
          // include: {
          //   package: {
          //     select: {
          //       id: true,
          //       name: true,
          //     },
          //   },
          // },
          orderBy,
          skip,
          take: limit,
        }),
        this.prisma.workflow_template.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      const data = await Promise.all(
        templates.map((template) => this.mapToResponseDto(template)),
      );

      this.logger.log(
        `Found ${total} workflow templates (page ${page}/${totalPages})`,
      );

      return {
        data,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch workflow templates: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find a single workflow template by ID
   *
   * @param id - Workflow template ID
   */
  async findOne(id: string): Promise<WorkflowTemplateResponseDto> {
    try {
      this.logger.log(`Fetching workflow template: ${id}`);

      const template = await this.prisma.workflow_template.findUnique({
        where: { id },
        // TODO: Re-enable package include after Prisma client regeneration
        // include: {
        //   package: {
        //     select: {
        //       id: true,
        //       name: true,
        //     },
        //   },
        // },
      });

      if (!template) {
        throw new NotFoundException(
          `Workflow template with ID ${id} not found`,
        );
      }

      return await this.mapToResponseDto(template);
    } catch (error) {
      this.logger.error(
        `Failed to fetch workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update a workflow template
   *
   * @param id - Workflow template ID
   * @param dto - Update data
   * @param adminUser - Admin user from JWT payload (optional for backward compatibility)
   */
  async update(
    id: string,
    dto: UpdateWorkflowTemplateDto,
    adminUser?: IJWTPayload | string,
  ): Promise<WorkflowTemplateResponseDto> {
    try {
      this.logger.log(`Updating workflow template: ${id}`);

      // Extract admin name from user object or fallback to string (backward compatibility)
      const adminName = this.extractAdminName(adminUser);

      // Check if template exists
      const existingTemplate = await this.prisma.workflow_template.findUnique({
        where: { id },
      });

      if (!existingTemplate) {
        throw new NotFoundException(
          `Workflow template with ID ${id} not found`,
        );
      }

      // Validate workflow template structure if provided
      if (dto.workflowTemplate) {
        this.validateWorkflowTemplate(dto.workflowTemplate);
      }

      // Check for name conflicts (excluding current template)
      if (dto.name) {
        const nameConflict = await this.prisma.workflow_template.findFirst({
          where: {
            name: {
              equals: dto.name,
              mode: 'insensitive',
            },
            id: {
              not: id,
            },
          },
        });

        if (nameConflict) {
          throw new ConflictException(
            `Workflow template with name "${dto.name}" already exists`,
          );
        }
      }

      // Multiple workflow templates are now allowed per service
      // This enables flexible workflow management for different scenarios

      const updateData: any = {
        updatedBy: adminName,
      };

      if (dto.name !== undefined) updateData.name = dto.name;
      if (dto.description !== undefined)
        updateData.description = dto.description;
      if (dto.serviceType !== undefined)
        updateData.serviceType = dto.serviceType;
      if (dto.serviceId !== undefined) updateData.serviceId = dto.serviceId;
      if (dto.isActive !== undefined) updateData.isActive = dto.isActive;
      if (dto.workflowTemplate !== undefined)
        updateData.workflowTemplate = dto.workflowTemplate as any;

      const updatedTemplate = await this.prisma.workflow_template.update({
        where: { id },
        data: updateData,
      });

      this.logger.log(
        `Workflow template updated successfully: ${id} by ${adminName || 'Unknown Admin'}`,
      );
      return await this.mapToResponseDto(updatedTemplate);
    } catch (error) {
      this.logger.error(
        `Failed to update workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Remove a workflow template
   *
   * @param id - Workflow template ID
   * @param adminUser - Admin user from JWT payload (optional for backward compatibility)
   */
  async remove(id: string, adminUser?: IJWTPayload | string): Promise<void> {
    try {
      this.logger.log(`Deleting workflow template: ${id}`);

      // Extract admin name from user object or fallback to string (backward compatibility)
      const adminName = this.extractAdminName(adminUser);

      // Check if template exists
      const existingTemplate = await this.prisma.workflow_template.findUnique({
        where: { id },
      });

      if (!existingTemplate) {
        throw new NotFoundException(
          `Workflow template with ID ${id} not found`,
        );
      }

      // Check if template is in use
      const usage = await this.checkUsage(id);
      if (!usage.canDelete) {
        throw new ConflictException(
          `Cannot delete workflow template. It is currently in use by ${usage.activeApplications} active applications.`,
        );
      }

      await this.prisma.workflow_template.delete({
        where: { id },
      });

      this.logger.log(
        `Workflow template deleted successfully: ${id} by ${adminName || 'Unknown Admin'}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to delete workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check usage of a workflow template
   *
   * @param id - Workflow template ID
   */
  async checkUsage(id: string): Promise<WorkflowTemplateUsageResponseDto> {
    try {
      this.logger.log(`Checking usage for workflow template: ${id}`);

      // Check if template exists
      const template = await this.prisma.workflow_template.findUnique({
        where: { id },
      });

      if (!template) {
        throw new NotFoundException(
          `Workflow template with ID ${id} not found`,
        );
      }

      // Count active applications using this template
      const activeApplications = await this.prisma.application.count({
        where: {
          workflow_template_id: id,
          status: {
            not: 'Completed',
          },
        },
      });

      // Count total applications that have used this template
      const totalApplications = await this.prisma.application.count({
        where: {
          workflow_template_id: id,
        },
      });

      // Get usage details by service type
      const usageByServiceType = await this.prisma.application.groupBy({
        by: ['service_type'],
        where: {
          workflow_template_id: id,
        },
        _count: {
          id: true,
        },
      });

      const usageDetails = usageByServiceType.map((item) => ({
        serviceType: item.service_type,
        count: item._count.id,
      }));

      const canDelete = activeApplications === 0;

      this.logger.log(
        `Usage check completed for template ${id}: ${activeApplications} active, ${totalApplications} total`,
      );

      return {
        templateId: id,
        activeApplications,
        totalApplications,
        canDelete,
        usageDetails,
      };
    } catch (error) {
      this.logger.error(
        `Failed to check template usage: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate workflow template structure
   *
   * @param workflowTemplate - Workflow template stages to validate
   */
  private validateWorkflowTemplate(workflowTemplate: any[]): void {
    if (!Array.isArray(workflowTemplate) || workflowTemplate.length === 0) {
      throw new BadRequestException(
        'Workflow template must contain at least one stage',
      );
    }

    // Check for duplicate stage orders
    const stageOrders = workflowTemplate.map((stage) => stage.stageOrder);
    const uniqueOrders = new Set(stageOrders);
    if (stageOrders.length !== uniqueOrders.size) {
      throw new BadRequestException(
        'Workflow template stages must have unique stage orders',
      );
    }

    // Validate stage orders are sequential starting from 1
    const sortedOrders = stageOrders.sort((a, b) => a - b);
    for (let i = 0; i < sortedOrders.length; i++) {
      if (sortedOrders[i] !== i + 1) {
        throw new BadRequestException(
          'Workflow template stage orders must be sequential starting from 1',
        );
      }
    }

    // Validate each stage
    workflowTemplate.forEach((stage, index) => {
      if (!stage.stageName || typeof stage.stageName !== 'string') {
        throw new BadRequestException(
          `Stage ${index + 1}: stageName is required and must be a string`,
        );
      }

      if (typeof stage.stageOrder !== 'number' || stage.stageOrder < 1) {
        throw new BadRequestException(
          `Stage ${index + 1}: stageOrder must be a positive number`,
        );
      }

      // Validate documents if provided
      if (stage.documents && Array.isArray(stage.documents)) {
        stage.documents.forEach((doc: any, docIndex: number) => {
          if (!doc.documentName || typeof doc.documentName !== 'string') {
            throw new BadRequestException(
              `Stage ${index + 1}, Document ${docIndex + 1}: documentName is required and must be a string`,
            );
          }
          if (typeof doc.required !== 'boolean') {
            throw new BadRequestException(
              `Stage ${index + 1}, Document ${docIndex + 1}: required must be a boolean`,
            );
          }
        });
      }

      // Validate custom form if provided
      if (stage.customForm && Array.isArray(stage.customForm)) {
        stage.customForm.forEach((field: any, fieldIndex: number) => {
          if (!field.fieldName || typeof field.fieldName !== 'string') {
            throw new BadRequestException(
              `Stage ${index + 1}, Field ${fieldIndex + 1}: fieldName is required and must be a string`,
            );
          }
          if (!field.fieldType || typeof field.fieldType !== 'string') {
            throw new BadRequestException(
              `Stage ${index + 1}, Field ${fieldIndex + 1}: fieldType is required and must be a string`,
            );
          }
          if (typeof field.required !== 'boolean') {
            throw new BadRequestException(
              `Stage ${index + 1}, Field ${fieldIndex + 1}: required must be a boolean`,
            );
          }
        });
      }
    });
  }



  /**
   * Extract admin name from user object or string
   *
   * @param adminUser - Admin user from JWT payload or string
   * @returns Admin name string
   */
  private extractAdminName(
    adminUser?: IJWTPayload | string,
  ): string | undefined {
    if (!adminUser) {
      return undefined;
    }

    if (typeof adminUser === 'string') {
      return adminUser;
    }

    // Extract name from JWT payload
    if (adminUser.sub && adminUser.sub.name) {
      return adminUser.sub.name;
    }

    // Fallback to email if name not available
    return adminUser.email || 'Unknown Admin';
  }

  /**
   * Map database entity to response DTO
   *
   * @param template - Database workflow template entity
   * @returns Workflow template response DTO
   */
  private async mapToResponseDto(
    template: any,
  ): Promise<WorkflowTemplateResponseDto> {
    return {
      id: template.id,
      name: template.name,
      description: template.description,
      serviceType: template.serviceType,
      serviceId: template.serviceId,
      packageId: template.packageId,
      packageName: template.package?.name,
      isActive: template.isActive,
      isDefault: template.isDefault || false,
      workflowTemplate: template.workflowTemplate,
      createdBy: template.createdBy,
      updatedBy: template.updatedBy,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  }

  /**
   * Validate that a package exists
   *
   * @param packageId - Package ID to validate
   */
  private async validatePackageExists(packageId: string): Promise<void> {
    const packageExists = await this.prisma.packages.findUnique({
      where: { id: packageId },
    });

    if (!packageExists) {
      throw new BadRequestException(
        `Package with ID ${packageId} not found`,
      );
    }
  }

  // TODO: Re-enable these methods after Prisma client regeneration
  /*
  async setDefaultTemplate(
    id: string,
    dto: SetDefaultTemplateDto,
    adminUser?: IJWTPayload | string,
  ): Promise<WorkflowTemplateResponseDto> {
    // Implementation will be added after Prisma client supports new fields
    throw new Error('Method not implemented yet - Prisma client needs regeneration');
  }

  async getDefaultTemplateForPackage(
    packageId: string,
  ): Promise<WorkflowTemplateResponseDto | null> {
    // Implementation will be added after Prisma client supports new fields
    throw new Error('Method not implemented yet - Prisma client needs regeneration');
  }
  */
}
