/**
 * Application Service Tests
 *
 * Tests for the simplified ApplicationService.
 * Ensures all core functionality works correctly.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { ApplicationStatus, PriorityLevel } from '@prisma/client';
import { CreateNewApplicationDto } from '../../src/application/dto/application.dto';

describe('ApplicationService', () => {
  let service: ApplicationService;
  let prismaService: PrismaService;
  let loggerService: LoggerService;

  const mockPrismaService = {
    application: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
    },
    payment: {
      findMany: jest.fn(),
    },
    agent: {
      findMany: jest.fn(),
    },
    workflow_template: {
      findUnique: jest.fn(),
    },
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: ApplicationFormService,
          useValue: {},
        },
        {
          provide: ApplicationDocumentService,
          useValue: {},
        },
        {
          provide: ApplicationTransformerService,
          useValue: {
            transformApplicationDetails: jest
              .fn()
              .mockImplementation((app) => app),
          },
        },
        {
          provide: MediaService,
          useValue: {},
        },
        {
          provide: DocumentVaultService,
          useValue: {},
        },
        {
          provide: NotificationService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);
    prismaService = module.get<PrismaService>(PrismaService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createApplicationFromPayment', () => {
    const mockPaymentData = {
      paymentId: 'payment_123',
      serviceType: 'immigration',
      serviceId: 'service_123',
      workflowTemplateId: 'template_123',
      userId: 'user_123',
    };

    it('should create a new application successfully', async () => {
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        status: ApplicationStatus.Draft,
        priority_level: PriorityLevel.Medium,
        user_id: 'user_123',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockPrismaService.application.findFirst.mockResolvedValue(null);
      mockPrismaService.application.count.mockResolvedValue(0);
      mockPrismaService.application.create.mockResolvedValue(mockApplication);

      const result =
        await service.createApplicationFromPayment(mockPaymentData);

      expect(result).toEqual(mockApplication);
      expect(mockPrismaService.application.findFirst).toHaveBeenCalledWith({
        where: { payment_id: 'payment_123' },
      });
      expect(mockPrismaService.application.create).toHaveBeenCalled();
    });

    it('should return existing application if already exists', async () => {
      const existingApplication = {
        id: 'app_existing',
        application_number: 'IMM-2024-000001',
        payment_id: 'payment_123',
      };

      mockPrismaService.application.findFirst.mockResolvedValue(
        existingApplication,
      );

      const result =
        await service.createApplicationFromPayment(mockPaymentData);

      expect(result).toEqual(existingApplication);
      expect(mockPrismaService.application.create).not.toHaveBeenCalled();
    });

    it('should handle guest user data', async () => {
      const guestPaymentData = {
        paymentId: 'payment_guest',
        serviceType: 'immigration',
        serviceId: 'service_123',
        guestName: 'John Doe',
        guestEmail: '<EMAIL>',
        guestMobile: '+1234567890',
      };

      const mockApplication = {
        id: 'app_guest',
        application_number: 'IMM-2024-000002',
        guest_name: 'John Doe',
        guest_email: '<EMAIL>',
        guest_mobile: '+1234567890',
      };

      mockPrismaService.application.findFirst.mockResolvedValue(null);
      mockPrismaService.application.count.mockResolvedValue(1);
      mockPrismaService.application.create.mockResolvedValue(mockApplication);

      const result =
        await service.createApplicationFromPayment(guestPaymentData);

      expect(result).toEqual(mockApplication);
      expect(mockPrismaService.application.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          guest_name: 'John Doe',
          guest_email: '<EMAIL>',
          guest_mobile: '+1234567890',
        }),
      });
    });
  });

  describe('getApplications', () => {
    it('should retrieve applications with filters', async () => {
      const mockApplications = [
        {
          id: 'app_1',
          application_number: 'IMM-2024-000001',
          service_type: 'immigration',
          status: ApplicationStatus.Draft,
          user: { id: 'user_1', name: 'John Doe', email: '<EMAIL>' },
        },
      ];

      mockPrismaService.application.findMany.mockResolvedValue(
        mockApplications,
      );
      mockPrismaService.application.count.mockResolvedValue(1);

      const filters = { service_type: 'immigration', user_id: 'user_1' };
      const result = await service.getApplications(filters, 1, 10);

      expect(result).toEqual({
        applications: mockApplications,
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });

      expect(mockPrismaService.application.findMany).toHaveBeenCalledWith({
        where: { service_type: 'immigration', user_id: 'user_1' },
        include: { user: { select: { id: true, name: true, email: true } } },
        orderBy: { created_at: 'desc' },
        skip: 0,
        take: 10,
      });
    });
  });

  describe('getApplicationById', () => {
    it('should retrieve application by ID', async () => {
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        user: { id: 'user_1', name: 'John Doe', email: '<EMAIL>' },
        payment: { id: 'payment_1', amount: 100, status: 'completed' },
        workflow_template: { id: 'template_1', name: 'Immigration Workflow' },
      };

      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );

      const result = await service.getApplicationById('app_123');

      expect(result).toEqual(mockApplication);
      expect(mockPrismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'app_123' },
        include: {
          user: { select: { id: true, name: true, email: true } },
          payment: { select: { id: true, amount: true, status: true } },
          workflow_template: {
            select: {
              id: true,
              name: true,
              description: true,
              workflowTemplate: true,
            },
          },
          documents: {
            include: {
              document: {
                select: {
                  id: true,
                  document_name: true,
                  original_filename: true,
                  file_path: true,
                  created_at: true,
                },
              },
            },
            orderBy: { updated_at: 'desc' },
          },
          form_data: {
            orderBy: { created_at: 'asc' },
          },
        },
      });
    });

    it('should throw NotFoundException when application not found', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      await expect(service.getApplicationById('nonexistent')).rejects.toThrow(
        'Application not found: nonexistent',
      );
    });
  });

  describe('validateUserAccess', () => {
    it('should return true for valid user access', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue({
        user_id: 'user_123',
      });

      const result = await service.validateUserAccess('app_123', 'user_123');

      expect(result).toBe(true);
    });

    it('should return false for invalid user access', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue({
        user_id: 'user_456',
      });

      const result = await service.validateUserAccess('app_123', 'user_123');

      expect(result).toBe(false);
    });

    it('should return false when application not found', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      const result = await service.validateUserAccess('app_123', 'user_123');

      expect(result).toBe(false);
    });
  });

  describe('createNewApplication', () => {
    const createApplicationDto: CreateNewApplicationDto = {
      service_type: 'immigration',
      service_id: 'service_123',
      user_id: 'user_456',
      priority_level: PriorityLevel.Medium,
      workflow_template_id: 'template_789',
      payments: ['payment_1', 'payment_2'],
      assigned_agent: ['agent_1', 'agent_2'],
    };

    const mockCreatedApplication = {
      id: 'app_123',
      application_number: 'IMM-2024-000001',
      service_type: 'immigration',
      service_id: 'service_123',
      user_id: 'user_456',
      priority_level: PriorityLevel.Medium,
      workflow_template_id: 'template_789',
      payment_ids: ['payment_1', 'payment_2'],
      agent_ids: ['agent_1', 'agent_2'],
      status: ApplicationStatus.Draft,
      current_step: '1',
      created_at: new Date(),
      updated_at: new Date(),
    };

    beforeEach(() => {
      // Reset all mocks
      jest.clearAllMocks();
    });

    it('should create application successfully with valid data', async () => {
      // Mock payment validation
      mockPrismaService.payment.findMany.mockResolvedValue([
        { id: 'payment_1' },
        { id: 'payment_2' },
      ]);

      // Mock agent validation
      mockPrismaService.agent.findMany.mockResolvedValue([
        { id: 'agent_1' },
        { id: 'agent_2' },
      ]);

      // Mock workflow template validation
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        id: 'template_789',
      });

      // Mock application creation
      mockPrismaService.application.create.mockResolvedValue(mockCreatedApplication);

      const result = await service.createNewApplication(createApplicationDto, 'admin_123');

      expect(mockPrismaService.payment.findMany).toHaveBeenCalledWith({
        where: { id: { in: ['payment_1', 'payment_2'] } },
        select: { id: true },
      });

      expect(mockPrismaService.agent.findMany).toHaveBeenCalledWith({
        where: { id: { in: ['agent_1', 'agent_2'] } },
        select: { id: true },
      });

      expect(mockPrismaService.workflow_template.findUnique).toHaveBeenCalledWith({
        where: { id: 'template_789' },
        select: { id: true },
      });

      expect(mockPrismaService.application.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          service_type: 'immigration',
          service_id: 'service_123',
          user_id: 'user_456',
          priority_level: PriorityLevel.Medium,
          workflow_template_id: 'template_789',
          payment_ids: ['payment_1', 'payment_2'],
          agent_ids: ['agent_1', 'agent_2'],
          status: 'Draft',
          current_step: '1',
          created_by: 'admin_123',
        }),
        include: expect.any(Object),
      });

      expect(result).toEqual(expect.objectContaining({
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        payment_ids: ['payment_1', 'payment_2'],
        agent_ids: ['agent_1', 'agent_2'],
      }));
    });

    it('should throw error for invalid payment IDs', async () => {
      // Mock payment validation - only one payment found
      mockPrismaService.payment.findMany.mockResolvedValue([
        { id: 'payment_1' },
      ]);

      await expect(
        service.createNewApplication(createApplicationDto, 'admin_123'),
      ).rejects.toThrow('One or more payment IDs are invalid');
    });

    it('should throw error for invalid agent IDs', async () => {
      // Mock payment validation - all payments found
      mockPrismaService.payment.findMany.mockResolvedValue([
        { id: 'payment_1' },
        { id: 'payment_2' },
      ]);

      // Mock agent validation - only one agent found
      mockPrismaService.agent.findMany.mockResolvedValue([
        { id: 'agent_1' },
      ]);

      await expect(
        service.createNewApplication(createApplicationDto, 'admin_123'),
      ).rejects.toThrow('One or more agent IDs are invalid');
    });

    it('should throw error for invalid workflow template ID', async () => {
      // Mock payment validation
      mockPrismaService.payment.findMany.mockResolvedValue([
        { id: 'payment_1' },
        { id: 'payment_2' },
      ]);

      // Mock agent validation
      mockPrismaService.agent.findMany.mockResolvedValue([
        { id: 'agent_1' },
        { id: 'agent_2' },
      ]);

      // Mock workflow template validation - not found
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(null);

      await expect(
        service.createNewApplication(createApplicationDto, 'admin_123'),
      ).rejects.toThrow('Invalid workflow template ID');
    });
  });
});
