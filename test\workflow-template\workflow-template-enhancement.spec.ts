/**
 * Workflow Template Enhancement Tests
 * 
 * Tests for the enhanced workflow template system with package integration
 * and default template logic.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowTemplateController } from '../../src/workflow-template/workflow-template.controller';
import { WorkflowTemplateService } from '../../src/workflow-template/workflow-template.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { IJWTPayload } from '../../src/types/auth';
import { JwtModule, JwtService } from '@nestjs/jwt';

describe('Workflow Template Enhancement', () => {
  let controller: WorkflowTemplateController;
  let service: WorkflowTemplateService;

  // Mock data for testing
  const mockWorkflowTemplate = {
    id: 'template_123',
    name: 'Immigration Workflow Template',
    description: 'Standard immigration process workflow',
    serviceType: 'packages',
    serviceId: null,
    packageId: 'package_123',
    isActive: true,
    isDefault: false,
    workflowTemplate: [
      { stageOrder: 1, stageName: 'Initial Review' },
      { stageOrder: 2, stageName: 'Document Collection' },
      { stageOrder: 3, stageName: 'Final Review' },
    ],
    createdBy: 'Admin User',
    updatedBy: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    package: {
      id: 'package_123',
      name: 'Premium Career Package',
    },
  };

  const mockPackage = {
    id: 'package_123',
    name: 'Premium Career Package',
    note: 'Comprehensive career development package',
    amount: 500,
    service: ['resume_review', 'interview_prep'],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  const mockAdmin: IJWTPayload = {
    id: 'admin_123',
    email: '<EMAIL>',
    sub: { name: 'Admin User' },
    iat: 1234567890,
    exp: 1234567890,
    tokenType: 'admin',
  };

  const mockPrismaService = {
    workflow_template: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    packages: {
      findUnique: jest.fn(),
    },
  };

  const mockLoggerService = {
    error: jest.fn(),
    warn: jest.fn(),
    log: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      controllers: [WorkflowTemplateController],
      providers: [
        WorkflowTemplateService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    controller = module.get<WorkflowTemplateController>(WorkflowTemplateController);
    service = module.get<WorkflowTemplateService>(WorkflowTemplateService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Package Integration', () => {
    it('should validate package existence when creating workflow template with packageId', async () => {
      // Arrange
      const createDto = {
        name: 'Test Workflow',
        description: 'Test workflow description',
        serviceType: 'packages',
        packageId: 'package_123',
        workflowTemplate: [
          { stageOrder: 1, stageName: 'Initial Review' },
        ],
      };

      mockPrismaService.packages.findUnique.mockResolvedValue(mockPackage);
      mockPrismaService.workflow_template.create.mockResolvedValue(mockWorkflowTemplate);

      // Act
      const result = await controller.create(createDto, mockAdmin);

      // Assert
      expect(mockPrismaService.packages.findUnique).toHaveBeenCalledWith({
        where: { id: 'package_123' },
      });
      expect(result).toBeDefined();
      expect(result.name).toBe('Immigration Workflow Template');
    });

    it('should throw error when package does not exist', async () => {
      // Arrange
      const createDto = {
        name: 'Test Workflow',
        description: 'Test workflow description',
        serviceType: 'packages',
        packageId: 'nonexistent_package',
        workflowTemplate: [
          { stageOrder: 1, stageName: 'Initial Review' },
        ],
      };

      mockPrismaService.packages.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(controller.create(createDto, mockAdmin)).rejects.toThrow(
        'Package with ID nonexistent_package not found'
      );
    });

    it('should allow creating workflow template without packageId', async () => {
      // Arrange
      const createDto = {
        name: 'General Workflow',
        description: 'General workflow without package',
        serviceType: 'immigration',
        serviceId: 'service_123',
        workflowTemplate: [
          { stageOrder: 1, stageName: 'Initial Review' },
        ],
      };

      const templateWithoutPackage = {
        ...mockWorkflowTemplate,
        packageId: null,
        package: null,
      };

      mockPrismaService.workflow_template.create.mockResolvedValue(templateWithoutPackage);

      // Act
      const result = await controller.create(createDto, mockAdmin);

      // Assert
      expect(mockPrismaService.packages.findUnique).not.toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('Enhanced Response Structure', () => {
    it('should include package name in workflow template response', async () => {
      // Arrange
      const templatesWithPackage = [mockWorkflowTemplate];
      
      mockPrismaService.workflow_template.findMany.mockResolvedValue(templatesWithPackage);
      mockPrismaService.workflow_template.count.mockResolvedValue(1);

      // Act
      const result = await controller.findAll({
        page: 1,
        limit: 10,
      });

      // Assert
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toHaveProperty('packageName');
      expect(result.data[0].packageName).toBe('Premium Career Package');
      expect(result.data[0]).toHaveProperty('packageId');
      expect(result.data[0].packageId).toBe('package_123');
      expect(result.data[0]).toHaveProperty('isDefault');
      expect(result.data[0].isDefault).toBe(false);
    });

    it('should handle workflow templates without packages', async () => {
      // Arrange
      const templateWithoutPackage = {
        ...mockWorkflowTemplate,
        packageId: null,
        package: null,
      };
      
      mockPrismaService.workflow_template.findMany.mockResolvedValue([templateWithoutPackage]);
      mockPrismaService.workflow_template.count.mockResolvedValue(1);

      // Act
      const result = await controller.findAll({
        page: 1,
        limit: 10,
      });

      // Assert
      expect(result.data).toHaveLength(1);
      expect(result.data[0].packageName).toBeUndefined();
      expect(result.data[0].packageId).toBeNull();
      expect(result.data[0]).toHaveProperty('isDefault');
    });
  });

  describe('Default Template Logic (Infrastructure)', () => {
    it('should include isDefault field in response DTO', async () => {
      // Arrange
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(mockWorkflowTemplate);

      // Act
      const result = await controller.findOne('template_123');

      // Assert
      expect(result).toHaveProperty('isDefault');
      expect(typeof result.isDefault).toBe('boolean');
    });

    it('should accept isDefault field in create DTO', async () => {
      // Arrange
      const createDto = {
        name: 'Default Workflow',
        description: 'Default workflow for package',
        serviceType: 'packages',
        packageId: 'package_123',
        isDefault: true,
        workflowTemplate: [
          { stageOrder: 1, stageName: 'Initial Review' },
        ],
      };

      mockPrismaService.packages.findUnique.mockResolvedValue(mockPackage);
      mockPrismaService.workflow_template.create.mockResolvedValue({
        ...mockWorkflowTemplate,
        isDefault: true,
      });

      // Act
      const result = await controller.create(createDto, mockAdmin);

      // Assert
      expect(result).toBeDefined();
      expect(result.isDefault).toBe(true);
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain existing workflow template functionality', async () => {
      // Arrange
      const legacyTemplate = {
        id: 'legacy_template',
        name: 'Legacy Workflow',
        description: 'Legacy workflow template',
        serviceType: 'immigration',
        serviceId: 'service_123',
        packageId: null,
        isActive: true,
        isDefault: false,
        workflowTemplate: [
          { stageOrder: 1, stageName: 'Review' },
        ],
        createdBy: 'Admin',
        updatedBy: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        package: null,
      };

      mockPrismaService.workflow_template.findMany.mockResolvedValue([legacyTemplate]);
      mockPrismaService.workflow_template.count.mockResolvedValue(1);

      // Act
      const result = await controller.findAll({
        page: 1,
        limit: 10,
      });

      // Assert
      expect(result.data).toHaveLength(1);
      expect(result.data[0].id).toBe('legacy_template');
      expect(result.data[0].serviceType).toBe('immigration');
      expect(result.data[0].packageId).toBeNull();
      expect(result.data[0].packageName).toBeUndefined();
    });

    it('should handle existing API parameters correctly', async () => {
      // Arrange
      const filters = {
        serviceType: 'immigration',
        isActive: true,
        page: 1,
        limit: 5,
      };

      mockPrismaService.workflow_template.findMany.mockResolvedValue([]);
      mockPrismaService.workflow_template.count.mockResolvedValue(0);

      // Act
      const result = await controller.findAll(filters);

      // Assert
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(5);
      expect(result.data).toEqual([]);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Arrange
      mockPrismaService.workflow_template.findMany.mockRejectedValue(
        new Error('Database connection failed')
      );

      // Act & Assert
      await expect(controller.findAll({ page: 1, limit: 10 })).rejects.toThrow(
        'Database connection failed'
      );
    });

    it('should validate workflow template structure', async () => {
      // Arrange
      const invalidDto = {
        name: 'Invalid Workflow',
        description: 'Invalid workflow template',
        serviceType: 'packages',
        packageId: 'package_123',
        workflowTemplate: 'invalid_structure', // Should be array
      };

      // Act & Assert
      await expect(controller.create(invalidDto as any, mockAdmin)).rejects.toThrow();
    });
  });
});
