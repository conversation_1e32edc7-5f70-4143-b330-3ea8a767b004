/**
 * Test script for enhanced GET /applications endpoint
 * Tests the new agent_ids and service_name fields in the response
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4242';

// Test configuration
const testConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
};

/**
 * Test the enhanced GET /applications endpoint
 */
async function testEnhancedApplicationsEndpoint() {
  console.log('🧪 Testing Enhanced GET /applications Endpoint');
  console.log('=' .repeat(50));

  try {
    // Test 1: Get applications without authentication (should work for public endpoints)
    console.log('\n📋 Test 1: Fetching applications list...');
    
    const response = await axios.get(`${BASE_URL}/applications`, {
      ...testConfig,
      params: {
        page: 1,
        limit: 5
      }
    });

    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Response structure:`, {
      hasData: !!response.data.data,
      hasPagination: !!response.data.pagination,
      dataLength: response.data.data?.length || 0
    });

    // Test 2: Verify response structure
    if (response.data.data && response.data.data.length > 0) {
      console.log('\n🔍 Test 2: Verifying response structure...');
      
      const firstApplication = response.data.data[0];
      const requiredFields = [
        'id',
        'application_number', 
        'service_type',
        'service_name',
        'status',
        'agent_ids',
        'created_at',
        'updated_at'
      ];

      const missingFields = requiredFields.filter(field => !(field in firstApplication));
      
      if (missingFields.length === 0) {
        console.log('✅ All required fields present');
        console.log(`📝 Sample application structure:`, {
          id: firstApplication.id,
          application_number: firstApplication.application_number,
          service_type: firstApplication.service_type,
          service_name: firstApplication.service_name,
          status: firstApplication.status,
          agent_ids_count: firstApplication.agent_ids?.length || 0,
          agent_ids_structure: firstApplication.agent_ids?.[0] ? 
            Object.keys(firstApplication.agent_ids[0]) : 'No agents assigned'
        });
      } else {
        console.log('❌ Missing fields:', missingFields);
      }

      // Test 3: Verify agent_ids structure
      console.log('\n👥 Test 3: Verifying agent_ids structure...');
      
      if (Array.isArray(firstApplication.agent_ids)) {
        console.log('✅ agent_ids is an array');
        
        if (firstApplication.agent_ids.length > 0) {
          const firstAgent = firstApplication.agent_ids[0];
          const agentRequiredFields = ['id', 'name', 'email'];
          const missingAgentFields = agentRequiredFields.filter(field => !(field in firstAgent));
          
          if (missingAgentFields.length === 0) {
            console.log('✅ Agent structure is correct');
            console.log(`👤 Sample agent:`, {
              id: firstAgent.id,
              name: firstAgent.name,
              email: firstAgent.email
            });
          } else {
            console.log('❌ Missing agent fields:', missingAgentFields);
          }
        } else {
          console.log('ℹ️  No agents assigned to this application');
        }
      } else {
        console.log('❌ agent_ids is not an array');
      }

      // Test 4: Verify service_name field
      console.log('\n🏷️  Test 4: Verifying service_name field...');
      
      if (firstApplication.service_name && typeof firstApplication.service_name === 'string') {
        console.log('✅ service_name is present and is a string');
        console.log(`🏷️  Service name: "${firstApplication.service_name}"`);
      } else {
        console.log('❌ service_name is missing or not a string');
      }

    } else {
      console.log('ℹ️  No applications found in the response');
    }

    console.log('\n🎉 Test completed successfully!');
    return true;

  } catch (error) {
    console.error('\n❌ Test failed:');
    
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Message: ${error.response.data?.message || 'Unknown error'}`);
      console.error(`Data:`, error.response.data);
    } else if (error.request) {
      console.error('No response received. Is the server running?');
      console.error('Make sure the server is running on http://localhost:4242');
    } else {
      console.error('Error:', error.message);
    }
    
    return false;
  }
}

/**
 * Test admin endpoint (if available)
 */
async function testAdminApplicationsEndpoint() {
  console.log('\n🔐 Testing Admin Applications Endpoint');
  console.log('=' .repeat(50));

  try {
    const response = await axios.get(`${BASE_URL}/applications/admin/all`, {
      ...testConfig,
      params: {
        page: 1,
        limit: 3
      }
    });

    console.log(`✅ Admin endpoint accessible - Status: ${response.status}`);
    console.log(`📊 Admin response structure:`, {
      hasData: !!response.data.data,
      dataLength: response.data.data?.length || 0
    });

    return true;

  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.log('🔒 Admin endpoint requires authentication (expected)');
      return true;
    } else {
      console.error('❌ Unexpected error with admin endpoint:', error.response?.status || error.message);
      return false;
    }
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Application Enhancement Tests');
  console.log('Time:', new Date().toISOString());
  console.log('Target:', BASE_URL);
  console.log('=' .repeat(60));

  const results = [];
  
  // Run tests
  results.push(await testEnhancedApplicationsEndpoint());
  results.push(await testAdminApplicationsEndpoint());

  // Summary
  console.log('\n📋 Test Summary');
  console.log('=' .repeat(30));
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testEnhancedApplicationsEndpoint,
  testAdminApplicationsEndpoint,
  runTests
};
