import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { SupabaseService } from '../utils/supabase.service';

interface IMedia {
  data: {
    path: string;
    id: string;
    fullPath: string;
  };

  error: any;
}

@Injectable()
export class MediaService {
  private readonly logger = new Logger(MediaService.name);
  private supabase = this.supabaseService.getClient();

  constructor(private supabaseService: SupabaseService) {
    this.validateConfiguration();
  }

  private validateConfiguration() {
    if (!process.env.BUCKET_NAME) {
      this.logger.error('BUCKET_NAME environment variable is not set');
      throw new Error('Storage bucket configuration is missing');
    }
    this.logger.log(`Using storage bucket: ${process.env.BUCKET_NAME}`);
  }

  async uploadFile(file: Express.Multer.File, folder: string) {
    try {
      this.logger.log(`Uploading file: ${file.originalname} to folder: ${folder}`);

      // Validate inputs
      if (!file) {
        throw new BadRequestException('No file provided');
      }

      if (!file.buffer || file.buffer.length === 0) {
        throw new BadRequestException('File buffer is empty');
      }

      if (!folder) {
        throw new BadRequestException('Folder path is required');
      }

      const date = Date.now();
      const fileName = `${folder}/${date}_${file.originalname}`;

      this.logger.debug(`Generated file path: ${fileName}`);
      this.logger.debug(`File size: ${file.size} bytes, MIME type: ${file.mimetype}`);

      // Test connection before upload
      const connectionTest = await this.supabaseService.testConnection();
      if (!connectionTest) {
        throw new BadRequestException('Storage service is not available');
      }

      const { data, error } = (await this.supabase.storage
        .from(process.env.BUCKET_NAME)
        .upload(fileName, file.buffer, {
          contentType: file.mimetype,
          upsert: false, // Prevent overwriting existing files
        })) as IMedia;

      if (error) {
        this.logger.error(`Supabase storage error:`, error);

        // Handle specific error types
        if (error.message?.includes('Duplicate')) {
          throw new BadRequestException('File with this name already exists');
        } else if (error.message?.includes('size')) {
          throw new BadRequestException('File size exceeds storage limits');
        } else if (error.message?.includes('type')) {
          throw new BadRequestException('File type not supported by storage');
        } else {
          throw new BadRequestException(`Storage upload failed: ${error.message || 'Unknown error'}`);
        }
      }

      if (!data || !data.fullPath) {
        this.logger.error('Upload succeeded but no file path returned');
        throw new BadRequestException('Upload completed but file path is missing');
      }

      this.logger.log(`File uploaded successfully: ${data.fullPath}`);

      return {
        status: 'OK',
        url: `${data.fullPath}`,
      };
    } catch (error) {
      this.logger.error(`Failed to upload file ${file?.originalname}:`, error);

      // Re-throw BadRequestException as-is
      if (error instanceof BadRequestException) {
        throw error;
      }

      // Handle network/connection errors
      if (error.message?.includes('fetch failed') || error.message?.includes('Network error')) {
        throw new BadRequestException('Unable to connect to storage service. Please check your internet connection and try again.');
      }

      if (error.message?.includes('timeout')) {
        throw new BadRequestException('Upload timed out. Please try again with a smaller file or check your connection.');
      }

      // Generic error fallback
      throw new BadRequestException(`File upload failed: ${error.message || 'Unknown error occurred'}`);
    }
  }

  /**
   * Upload file for application with enhanced folder structure
   * Uses format: documents/{applicationID}/{sanitized-filename}
   */
  async uploadApplicationDocument(
    file: Express.Multer.File,
    applicationId: string,
    options?: {
      preserveOriginalName?: boolean;
      addTimestamp?: boolean;
    }
  ) {
    try {
      this.logger.log(`Uploading application document: ${file.originalname} for application: ${applicationId}`);

      if (!file) {
        throw new BadRequestException('No file provided');
      }

      if (!file.buffer || file.buffer.length === 0) {
        throw new BadRequestException('File buffer is empty');
      }

      if (!applicationId) {
        throw new BadRequestException('Application ID is required');
      }

      // Sanitize filename to remove special characters and ensure uniqueness
      const sanitizedFilename = this.sanitizeFilename(file.originalname);

      // Generate unique filename to prevent conflicts
      const timestamp = options?.addTimestamp !== false ? Date.now() : '';
      const uniqueId = Math.random().toString(36).substring(2, 8);

      let finalFilename: string;
      if (options?.preserveOriginalName && !options?.addTimestamp) {
        finalFilename = sanitizedFilename;
      } else if (options?.preserveOriginalName) {
        const ext = sanitizedFilename.split('.').pop();
        const nameWithoutExt = sanitizedFilename.replace(`.${ext}`, '');
        finalFilename = `${nameWithoutExt}_${timestamp}_${uniqueId}.${ext}`;
      } else {
        finalFilename = `${timestamp}_${uniqueId}_${sanitizedFilename}`;
      }

      // Create application-specific folder structure
      const folderPath = `documents/${applicationId}`;
      const fileName = `${folderPath}/${finalFilename}`;

      this.logger.debug(`Generated application document path: ${fileName}`);
      this.logger.debug(`File size: ${file.size} bytes, MIME type: ${file.mimetype}`);

      // Test connection before upload
      const connectionTest = await this.supabaseService.testConnection();
      if (!connectionTest) {
        throw new BadRequestException('Storage service is not available');
      }

      const { data, error } = (await this.supabase.storage
        .from(process.env.BUCKET_NAME)
        .upload(fileName, file.buffer, {
          contentType: file.mimetype,
          upsert: false, // Prevent overwriting existing files
        })) as IMedia;

      if (error) {
        this.logger.error(`Supabase storage error:`, error);

        // Handle specific error types
        if (error.message?.includes('Duplicate')) {
          // If duplicate, try with a new unique ID
          const newUniqueId = Math.random().toString(36).substring(2, 8);
          const ext = sanitizedFilename.split('.').pop();
          const nameWithoutExt = sanitizedFilename.replace(`.${ext}`, '');
          const retryFilename = `${folderPath}/${timestamp}_${newUniqueId}_${nameWithoutExt}.${ext}`;

          this.logger.warn(`File name conflict, retrying with: ${retryFilename}`);

          const { data: retryData, error: retryError } = (await this.supabase.storage
            .from(process.env.BUCKET_NAME)
            .upload(retryFilename, file.buffer, {
              contentType: file.mimetype,
              upsert: false,
            })) as IMedia;

          if (retryError) {
            throw new BadRequestException(`File upload failed after retry: ${retryError.message}`);
          }

          if (!retryData || !retryData.fullPath) {
            throw new BadRequestException('Upload completed but file path is missing');
          }

          this.logger.log(`Application document uploaded successfully after retry: ${retryData.fullPath}`);
          return {
            status: 'OK',
            url: `${retryData.fullPath}`,
            applicationId,
            originalFilename: file.originalname,
            sanitizedFilename: finalFilename,
          };
        } else if (error.message?.includes('size')) {
          throw new BadRequestException('File size exceeds storage limits');
        } else if (error.message?.includes('type')) {
          throw new BadRequestException('File type not supported by storage');
        } else {
          throw new BadRequestException(`Storage upload failed: ${error.message || 'Unknown error'}`);
        }
      }

      if (!data || !data.fullPath) {
        this.logger.error('Upload succeeded but no file path returned');
        throw new BadRequestException('Upload completed but file path is missing');
      }

      this.logger.log(`Application document uploaded successfully: ${data.fullPath}`);

      return {
        status: 'OK',
        url: `${data.fullPath}`,
        applicationId,
        originalFilename: file.originalname,
        sanitizedFilename: finalFilename,
      };
    } catch (error) {
      this.logger.error(`Failed to upload application document ${file?.originalname}:`, error);

      // Re-throw BadRequestException as-is
      if (error instanceof BadRequestException) {
        throw error;
      }

      // Handle network/connection errors
      if (error.message?.includes('fetch failed') || error.message?.includes('Network error')) {
        throw new BadRequestException('Unable to connect to storage service. Please check your internet connection and try again.');
      }

      if (error.message?.includes('timeout')) {
        throw new BadRequestException('Upload timed out. Please try again with a smaller file or check your connection.');
      }

      // Generic error fallback
      throw new BadRequestException(`Application document upload failed: ${error.message || 'Unknown error occurred'}`);
    }
  }

  /**
   * Sanitize filename to remove special characters and ensure safe storage
   */
  private sanitizeFilename(filename: string): string {
    // Remove or replace special characters that might cause issues
    const sanitized = filename
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .toLowerCase(); // Convert to lowercase for consistency

    // Ensure filename has an extension
    if (!sanitized.includes('.')) {
      return `${sanitized}.bin`; // Add generic extension if none exists
    }

    // Limit filename length (keeping extension)
    const parts = sanitized.split('.');
    const extension = parts.pop();
    const nameWithoutExt = parts.join('.');

    if (nameWithoutExt.length > 100) {
      return `${nameWithoutExt.substring(0, 100)}.${extension}`;
    }

    return sanitized;
  }

  async uploadFiles(files: Array<Express.Multer.File>, folder: string) {
    try {
      this.logger.log(`Uploading ${files.length} files to folder: ${folder}`);

      if (!files || files.length === 0) {
        throw new BadRequestException('No files provided');
      }

      if (files.length > 10) {
        throw new BadRequestException('Maximum number of files allowed is 10');
      }

      // Test connection before upload
      const connectionTest = await this.supabaseService.testConnection();
      if (!connectionTest) {
        throw new BadRequestException('Storage service is not available');
      }

      const uploadPromises = files.map(async (file, index) => {
        try {
          this.logger.debug(`Uploading file ${index + 1}/${files.length}: ${file.originalname}`);

          const date = Date.now();
          const fileName = `${folder}/${date}_${index}_${file.originalname}`;

          const { data, error } = (await this.supabase.storage
            .from(process.env.BUCKET_NAME)
            .upload(fileName, file.buffer, {
              contentType: file.mimetype,
              upsert: false,
            })) as IMedia;

          if (error) {
            this.logger.error(`Failed to upload file ${file.originalname}:`, error);
            throw new BadRequestException(`Failed to upload ${file.originalname}: ${error.message}`);
          }

          if (!data || !data.fullPath) {
            throw new BadRequestException(`Upload of ${file.originalname} completed but file path is missing`);
          }

          this.logger.debug(`File ${file.originalname} uploaded successfully: ${data.fullPath}`);
          return `${data.fullPath}`;
        } catch (error) {
          this.logger.error(`Error uploading file ${file.originalname}:`, error);
          throw error;
        }
      });

      const results = await Promise.all(uploadPromises);
      this.logger.log(`Successfully uploaded ${results.length} files`);

      return {
        status: 'OK',
        url: results,
      };
    } catch (error) {
      this.logger.error(`Failed to upload files:`, error);

      if (error instanceof BadRequestException) {
        throw error;
      }

      if (error.message?.includes('fetch failed') || error.message?.includes('Network error')) {
        throw new BadRequestException('Unable to connect to storage service. Please check your internet connection and try again.');
      }

      throw new BadRequestException(`Multiple file upload failed: ${error.message || 'Unknown error occurred'}`);
    }
  }
}
